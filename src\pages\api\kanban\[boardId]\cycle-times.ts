/**
 * Kanban Cycle Times API endpoint
 * Following Clean Code: Single responsibility, express intent
 * Following Clean Architecture: API layer with dependency injection
 */

import type { APIRoute } from 'astro';
import { getMcpAtlassianClient } from '../../../../lib/mcp/atlassian.js';
import { CycleTimeCalculator } from '../../../../lib/kanban/cycle-time-calculator.js';
import { initializeRepositoryFactory } from '../../../../lib/database/repository-factory.js';

/**
 * API response for cycle times calculation
 * Following Clean Code: Express intent, type safety
 */
interface CycleTimesResponse {
  boardId: string;
  boardName?: string;
  cycleTimes: Array<{
    issueKey: string;
    startDate: string | null;
    completionDate: string | null;
    cycleTimeDays: number | null;
    issueType: string;
  }>;
  stats: {
    totalIssues: number;
    completedIssues: number;
    inProgressIssues: number;
    averageCycleTime: number | null;
  };
  timestamp: string;
}

/**
 * GET /api/kanban/[boardId]/cycle-times
 * Calculates and returns cycle times for board issues
 * Following Clean Code: Single responsibility, error handling
 */
export const GET: APIRoute = async ({ params, url }) => {
  const boardId = params.boardId;
  
  if (!boardId) {
    return new Response(
      JSON.stringify({ error: 'Board ID is required' }),
      { 
        status: 400, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Parse query parameters
    const maxResults = parseInt(url.searchParams.get('maxResults') || '20');
    const forceRefresh = url.searchParams.get('forceRefresh') === 'true';
    
    console.log(`[CYCLE-TIMES-API] Processing request for board ${boardId}, maxResults: ${maxResults}, forceRefresh: ${forceRefresh}`);

    // Initialize dependencies
    const mcpClient = getMcpAtlassianClient();
    const repositoryFactory = await initializeRepositoryFactory();
    const cycleTimesRepo = repositoryFactory.createIssueCycleTimesRepository();

    // Get API clients
    const apiClient = mcpClient.getApiClient();
    const changelogApi = mcpClient.getChangelogApi();
    const boardsApi = mcpClient.getBoardsApi();
    
    // Initialize cycle time calculator
    const calculator = new CycleTimeCalculator(apiClient, changelogApi, boardsApi);

    let cycleTimes;
    let boardName = 'Unknown Board';

    // Get board details for name
    try {
      const boardDetails = await boardsApi.fetchBoardDetails(boardId);
      boardName = boardDetails.name;
    } catch (error) {
      console.warn(`Failed to fetch board details for ${boardId}:`, error);
    }

    if (forceRefresh) {
      console.log(`[CYCLE-TIMES-API] Force refresh requested, calculating fresh cycle times`);
      
      // Calculate fresh cycle times
      cycleTimes = await calculator.calculateBatchCycleTimes(boardId, maxResults);
      
      // Save to database
      await cycleTimesRepo.saveBatchCycleTimes(cycleTimes);
      
    } else {
      console.log(`[CYCLE-TIMES-API] Checking database for existing cycle times`);
      
      // Check if we have data in database
      const existingCycleTimes = await cycleTimesRepo.getBoardCycleTimes(boardId);
      
      if (existingCycleTimes.length >= maxResults) {
        console.log(`[CYCLE-TIMES-API] Using ${existingCycleTimes.length} cycle times from database`);
        cycleTimes = existingCycleTimes.slice(0, maxResults);
      } else {
        console.log(`[CYCLE-TIMES-API] Insufficient data in database (${existingCycleTimes.length}), calculating fresh cycle times`);
        
        // Calculate fresh cycle times
        cycleTimes = await calculator.calculateBatchCycleTimes(boardId, maxResults);
        
        // Save to database
        await cycleTimesRepo.saveBatchCycleTimes(cycleTimes);
      }
    }

    // Calculate statistics
    const completedCycleTimes = cycleTimes.filter(ct => ct.cycleTimeDays !== null);
    const averageCycleTime = completedCycleTimes.length > 0
      ? completedCycleTimes.reduce((sum, ct) => sum + (ct.cycleTimeDays || 0), 0) / completedCycleTimes.length
      : null;

    const stats = {
      totalIssues: cycleTimes.length,
      completedIssues: completedCycleTimes.length,
      inProgressIssues: cycleTimes.length - completedCycleTimes.length,
      averageCycleTime: averageCycleTime ? Math.round(averageCycleTime * 10) / 10 : null,
    };

    // Log results to console as requested
    console.log(`\n=== KANBAN CYCLE TIMES RESULTS ===`);
    console.log(`Loading Kanban analytics for board: ${boardId}`);
    cycleTimes.forEach(ct => {
      if (ct.cycleTimeDays !== null) {
        console.log(`Issue ${ct.issueKey}: ${ct.cycleTimeDays} days (${ct.issueType})`);
      } else {
        console.log(`Issue ${ct.issueKey}: null (in progress) (${ct.issueType})`);
      }
    });
    console.log(`Processed ${stats.totalIssues} issues, ${stats.completedIssues} completed, ${stats.inProgressIssues} in progress`);
    console.log(`Average cycle time: ${stats.averageCycleTime ? stats.averageCycleTime + ' days' : 'N/A'}`);
    console.log(`=====================================\n`);

    const response: CycleTimesResponse = {
      boardId,
      boardName,
      cycleTimes: cycleTimes.map(ct => ({
        issueKey: ct.issueKey,
        startDate: ct.startDate,
        completionDate: ct.completionDate,
        cycleTimeDays: ct.cycleTimeDays,
        issueType: ct.issueType,
      })),
      stats,
      timestamp: new Date().toISOString(),
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error(`[CYCLE-TIMES-API] Error processing cycle times for board ${boardId}:`, error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to calculate cycle times',
        details: error instanceof Error ? error.message : 'Unknown error',
        boardId,
        timestamp: new Date().toISOString(),
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
