---
/**
 * Kanban Analytics Component
 * Following Clean Code: Single responsibility - Kanban-specific cycle time analytics
 * Placeholder component for future Kanban analytics implementation
 */

export interface Props {
  boardId: string | null;
  loading?: boolean;
}

const { boardId, loading = false } = Astro.props;
---

<div class="kanban-analytics">
  <!-- Kanban Analytics Content -->
  <div class="kanban-content-section">
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
      <!-- Main Cycle Time Chart -->
      <div class="xl:col-span-2">
        <div id="cycle-time-chart-container">
          {loading ? (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Cycle Time Analysis
              </h3>
              <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div class="h-32 bg-gray-200 rounded mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ) : (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Cycle Time Analysis
              </h3>
              <div class="text-center py-12">
                <div class="text-6xl mb-4">⏱️</div>
                <h4 class="text-xl font-medium text-gray-900 mb-2">
                  Kanban Analytics Coming Soon
                </h4>
                <p class="text-gray-600 mb-6">
                  Cycle time percentiles, throughput metrics, and flow efficiency analysis will be available here.
                </p>
                <div class="bg-blue-50 p-4 rounded-lg text-left">
                  <h5 class="font-medium text-blue-900 mb-2">Planned Features:</h5>
                  <ul class="text-sm text-blue-800 space-y-1">
                    <li>• Cycle time percentiles (50th, 75th, 85th, 95th)</li>
                    <li>• Issue type filtering (Bug, Story, Task)</li>
                    <li>• Swimlane filtering with custom JQL</li>
                    <li>• Throughput and flow efficiency metrics</li>
                    <li>• Lead time vs cycle time analysis</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <!-- Current Flow Metrics -->
      <div class="xl:col-span-1">
        <div id="flow-metrics-container">
          {loading ? (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Flow Metrics
              </h3>
              <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
                <div class="h-8 bg-gray-200 rounded mb-4"></div>
                <div class="grid grid-cols-2 gap-4">
                  <div class="h-16 bg-gray-200 rounded"></div>
                  <div class="h-16 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ) : (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Flow Metrics
              </h3>
              <div class="text-center py-8">
                <div class="text-4xl mb-3">📊</div>
                <p class="text-gray-600 text-sm">
                  Flow metrics will show throughput, WIP limits, and cycle time trends.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    
    <!-- Kanban Insights Section -->
    <div class="mt-8">
      <div id="kanban-insights-container">
        {loading ? (
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Flow Insights
            </h3>
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ) : (
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Flow Insights
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="insight-card p-4 bg-purple-50 rounded-lg">
                <div class="flex items-start">
                  <div class="text-purple-500 mr-3 mt-1">🔄</div>
                  <div>
                    <h5 class="font-medium text-purple-900 mb-1">Continuous Flow</h5>
                    <p class="text-sm text-purple-800">
                      Kanban boards focus on continuous delivery and flow optimization rather than sprint-based velocity.
                    </p>
                  </div>
                </div>
              </div>
              <div class="insight-card p-4 bg-green-50 rounded-lg">
                <div class="flex items-start">
                  <div class="text-green-500 mr-3 mt-1">⚡</div>
                  <div>
                    <h5 class="font-medium text-green-900 mb-1">Cycle Time Focus</h5>
                    <p class="text-sm text-green-800">
                      Measure how long it takes to complete work items from start to finish.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  </div>
</div>

<style>
  .kanban-analytics {
    @apply w-full;
  }
  
  .kanban-content-section {
    @apply w-full;
  }
  
  .insight-card {
    @apply transition-transform duration-200;
  }
  
  .insight-card:hover {
    @apply transform scale-105;
  }
</style>

<script>
  // Kanban Analytics Client-side Logic
  // Following Clean Code: Express intent, single responsibility per function
  
  let currentBoardId: string | null = null;
  
  /**
   * Loads Kanban analytics data for a specific board
   * Following Clean Code: Express intent, single responsibility
   */
  async function loadKanbanAnalytics(boardId: string) {
    if (!boardId || boardId === currentBoardId) return;

    currentBoardId = boardId;
    console.log('Loading Kanban analytics for board:', boardId);

    try {
      // Show loading state
      showLoadingState();

      // Call cycle times API
      const response = await fetch(`/api/kanban/${boardId}/cycle-times?maxResults=20`);

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Update UI with cycle times data
      updateCycleTimeChart(data);
      updateFlowMetrics(data);
      updateKanbanInsights(data);

      console.log('Kanban analytics loaded successfully for board:', boardId);

    } catch (error) {
      console.error('Failed to load Kanban analytics:', error);
      showErrorState(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      hideLoadingState();
    }
  }

  /**
   * Shows loading state for all components
   * Following Clean Code: Express intent, UI state management
   */
  function showLoadingState() {
    const containers = [
      'cycle-time-chart-container',
      'flow-metrics-container',
      'kanban-insights-container'
    ];

    containers.forEach(containerId => {
      const container = document.getElementById(containerId);
      if (container) {
        container.innerHTML = createLoadingHTML(containerId);
      }
    });
  }

  /**
   * Hides loading state
   * Following Clean Code: Express intent, UI state management
   */
  function hideLoadingState() {
    // Loading state is replaced by actual content in update functions
  }

  /**
   * Shows error state
   * Following Clean Code: Express intent, error handling
   */
  function showErrorState(errorMessage: string) {
    const containers = [
      'cycle-time-chart-container',
      'flow-metrics-container',
      'kanban-insights-container'
    ];

    containers.forEach(containerId => {
      const container = document.getElementById(containerId);
      if (container) {
        container.innerHTML = createErrorHTML(errorMessage);
      }
    });
  }
  
  /**
   * Updates cycle time chart with data
   * Following Clean Code: Express intent, single responsibility
   */
  function updateCycleTimeChart(data: any) {
    const container = document.getElementById('cycle-time-chart-container');
    if (!container) return;

    const { cycleTimes, stats } = data;
    const completedIssues = cycleTimes.filter((ct: any) => ct.cycleTimeDays !== null);

    container.innerHTML = `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          Cycle Time Analysis
        </h3>
        <div class="mb-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">${stats.totalIssues}</div>
              <div class="text-sm text-gray-600">Total Issues</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">${stats.completedIssues}</div>
              <div class="text-sm text-gray-600">Completed</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">${stats.inProgressIssues}</div>
              <div class="text-sm text-gray-600">In Progress</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">${stats.averageCycleTime || 'N/A'}</div>
              <div class="text-sm text-gray-600">Avg Days</div>
            </div>
          </div>
        </div>

        <div class="space-y-2 max-h-64 overflow-y-auto">
          <h4 class="font-medium text-gray-900 mb-2">Recent Issues (First 20)</h4>
          ${cycleTimes.map((ct: any) => `
            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
              <div class="flex items-center space-x-3">
                <span class="font-mono text-sm text-blue-600">${ct.issueKey}</span>
                <span class="text-xs px-2 py-1 bg-gray-200 rounded">${ct.issueType}</span>
              </div>
              <div class="text-right">
                ${ct.cycleTimeDays !== null
                  ? `<span class="font-medium text-green-600">${ct.cycleTimeDays} days</span>`
                  : `<span class="text-orange-600">In Progress</span>`
                }
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  /**
   * Updates flow metrics with data
   * Following Clean Code: Express intent, single responsibility
   */
  function updateFlowMetrics(data: any) {
    const container = document.getElementById('flow-metrics-container');
    if (!container) return;

    const { stats } = data;
    const completionRate = stats.totalIssues > 0
      ? Math.round((stats.completedIssues / stats.totalIssues) * 100)
      : 0;

    container.innerHTML = `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          Flow Metrics
        </h3>
        <div class="space-y-4">
          <div class="metric-item">
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm font-medium text-gray-700">Completion Rate</span>
              <span class="text-sm font-bold text-green-600">${completionRate}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-500 h-2 rounded-full" style="width: ${completionRate}%"></div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-blue-50 rounded-lg">
              <div class="text-lg font-bold text-blue-600">${stats.averageCycleTime || 'N/A'}</div>
              <div class="text-xs text-blue-800">Avg Cycle Time</div>
            </div>
            <div class="text-center p-3 bg-purple-50 rounded-lg">
              <div class="text-lg font-bold text-purple-600">${stats.completedIssues}</div>
              <div class="text-xs text-purple-800">Throughput</div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Updates Kanban insights with data
   * Following Clean Code: Express intent, single responsibility
   */
  function updateKanbanInsights(data: any) {
    const container = document.getElementById('kanban-insights-container');
    if (!container) return;

    const { cycleTimes, stats } = data;
    const issueTypes = [...new Set(cycleTimes.map((ct: any) => ct.issueType))];

    container.innerHTML = `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibent text-gray-900 mb-4">
          Flow Insights
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="insight-card p-4 bg-green-50 rounded-lg">
            <div class="flex items-start">
              <div class="text-green-500 mr-3 mt-1">📊</div>
              <div>
                <h5 class="font-medium text-green-900 mb-1">Issue Types</h5>
                <p class="text-sm text-green-800 mb-2">
                  Found ${issueTypes.length} different issue types in this board.
                </p>
                <div class="flex flex-wrap gap-1">
                  ${issueTypes.map(type => `
                    <span class="text-xs px-2 py-1 bg-green-200 text-green-800 rounded">${type}</span>
                  `).join('')}
                </div>
              </div>
            </div>
          </div>

          <div class="insight-card p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
              <div class="text-blue-500 mr-3 mt-1">⚡</div>
              <div>
                <h5 class="font-medium text-blue-900 mb-1">Flow Status</h5>
                <p class="text-sm text-blue-800">
                  ${stats.completedIssues} of ${stats.totalIssues} issues completed.
                  ${stats.averageCycleTime ? `Average cycle time: ${stats.averageCycleTime} days.` : 'No completed issues yet.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Creates loading HTML for a container
   * Following Clean Code: Express intent, reusable component
   */
  function createLoadingHTML(containerId: string): string {
    const titles = {
      'cycle-time-chart-container': 'Cycle Time Analysis',
      'flow-metrics-container': 'Flow Metrics',
      'kanban-insights-container': 'Flow Insights'
    };

    const title = titles[containerId as keyof typeof titles] || 'Loading';

    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">${title}</h3>
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div class="h-32 bg-gray-200 rounded mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    `;
  }

  /**
   * Creates error HTML for a container
   * Following Clean Code: Express intent, error handling
   */
  function createErrorHTML(errorMessage: string): string {
    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Error</h3>
        <div class="text-center py-8">
          <div class="text-4xl mb-3">⚠️</div>
          <p class="text-red-600 text-sm mb-4">${errorMessage}</p>
          <button onclick="location.reload()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            Retry
          </button>
        </div>
      </div>
    `;
  }

  // Export functions for parent component access
  (window as any).kanbanAnalytics = {
    loadKanbanAnalytics
  };
</script>
