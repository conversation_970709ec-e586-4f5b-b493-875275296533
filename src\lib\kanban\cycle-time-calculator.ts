/**
 * Kanban Cycle Time Calculator
 * Following Clean Code: Single responsibility, express intent
 * Following Clean Architecture: Domain logic separated from infrastructure
 */

import type { JiraApiClient } from '../jira/api-client.js';
import type { JiraChangelogApi } from '../jira/changelog-api.js';
import type { JiraBoardsApi } from '../jira/boards-api.js';

/**
 * Issue cycle time data structure
 * Following Clean Code: Intention-revealing names, immutability
 */
export interface IssueCycleTime {
  readonly issueKey: string;
  readonly boardId: string;
  readonly startDate: string | null; // ISO date when issue enters "To Do"
  readonly completionDate: string | null; // ISO date when issue reaches "Done"
  readonly cycleTimeDays: number | null; // Cycle time in days (can be decimal)
  readonly issueType: string;
}

/**
 * Board issue data from JIRA API
 * Following Clean Code: Express intent, type safety
 */
export interface BoardIssue {
  readonly key: string;
  readonly fields: {
    readonly issuetype: {
      readonly name: string;
    };
    readonly status: {
      readonly id: string;
      readonly name: string;
    };
  };
}

/**
 * Board column configuration for cycle time calculation
 * Following Clean Code: Single responsibility for column mapping
 */
export interface BoardColumnConfig {
  readonly boardId: string;
  readonly toDoStatusIds: readonly string[]; // First column status IDs
  readonly doneStatusIds: readonly string[]; // Done column status IDs
}

/**
 * Cycle time calculator service
 * Following Clean Code: Single responsibility, dependency injection
 */
export class CycleTimeCalculator {
  constructor(
    private readonly apiClient: JiraApiClient,
    private readonly changelogApi: JiraChangelogApi,
    private readonly boardsApi: JiraBoardsApi
  ) {}

  /**
   * Fetches board issues with pagination support
   * Following Clean Code: Express intent, single responsibility
   */
  async fetchBoardIssues(boardId: string, maxResults: number = 20): Promise<BoardIssue[]> {
    const endpoint = `/rest/agile/1.0/board/${boardId}/issue?maxResults=${maxResults}&fields=key,issuetype,status`;
    
    try {
      const response = await this.apiClient.get(endpoint);
      const data = response.data as any;
      
      if (!data.issues || !Array.isArray(data.issues)) {
        console.warn(`No issues found for board ${boardId}`);
        return [];
      }

      return data.issues.map((issue: any) => ({
        key: issue.key,
        fields: {
          issuetype: {
            name: issue.fields?.issuetype?.name || 'Unknown'
          },
          status: {
            id: issue.fields?.status?.id || '',
            name: issue.fields?.status?.name || 'Unknown'
          }
        }
      }));
    } catch (error) {
      console.error(`Failed to fetch board issues for ${boardId}:`, error);
      throw new Error(`Failed to fetch board issues: ${error}`);
    }
  }

  /**
   * Gets board column configuration for cycle time calculation
   * Following Clean Code: Express intent, defensive programming
   */
  async getBoardColumnConfig(boardId: string): Promise<BoardColumnConfig> {
    try {
      const boardConfig = await this.boardsApi.fetchBoardConfiguration(boardId);
      
      if (!boardConfig.columns || boardConfig.columns.length === 0) {
        throw new Error(`No columns found for board ${boardId}`);
      }

      // Find first column (To Do) - typically the first column in the configuration
      const firstColumn = boardConfig.columns[0];
      const toDoStatusIds = firstColumn.statuses.map(status => status.id);

      // Find Done columns - typically columns with "Done" in the name
      const doneColumns = boardConfig.columns.filter(column => 
        column.name.toLowerCase().includes('done') || 
        column.name.toLowerCase().includes('complete')
      );

      if (doneColumns.length === 0) {
        // Fallback: use last column as Done
        const lastColumn = boardConfig.columns[boardConfig.columns.length - 1];
        const doneStatusIds = lastColumn.statuses.map(status => status.id);
        
        console.warn(`No "Done" columns found for board ${boardId}, using last column: ${lastColumn.name}`);
        
        return {
          boardId,
          toDoStatusIds,
          doneStatusIds
        };
      }

      // Collect all status IDs from Done columns
      const doneStatusIds = doneColumns.flatMap(column => 
        column.statuses.map(status => status.id)
      );

      return {
        boardId,
        toDoStatusIds,
        doneStatusIds
      };
    } catch (error) {
      console.error(`Failed to get board column config for ${boardId}:`, error);
      throw new Error(`Failed to get board configuration: ${error}`);
    }
  }

  /**
   * Calculates cycle time for a single issue
   * Following Clean Code: Express intent, single responsibility
   */
  async calculateIssueCycleTime(
    issueKey: string, 
    boardId: string, 
    issueType: string,
    columnConfig: BoardColumnConfig
  ): Promise<IssueCycleTime> {
    try {
      // Get issue changelog
      const changelog = await this.changelogApi.fetchIssueChangelog(issueKey);
      
      // Find when issue entered To Do (start date)
      const startDate = await this.findToDoEntryDate(issueKey, columnConfig.toDoStatusIds);
      
      // Find when issue reached Done (completion date)
      const completionDate = await this.changelogApi.findStatusTransitionDateById(
        issueKey, 
        columnConfig.doneStatusIds
      );

      // Calculate cycle time if both dates exist
      let cycleTimeDays: number | null = null;
      if (startDate && completionDate) {
        cycleTimeDays = this.calculateDaysDifference(startDate, completionDate);
      }

      return {
        issueKey,
        boardId,
        startDate,
        completionDate,
        cycleTimeDays,
        issueType
      };
    } catch (error) {
      console.error(`Failed to calculate cycle time for ${issueKey}:`, error);
      
      // Return partial data on error
      return {
        issueKey,
        boardId,
        startDate: null,
        completionDate: null,
        cycleTimeDays: null,
        issueType
      };
    }
  }

  /**
   * Finds when an issue entered the To Do column
   * Following Clean Code: Express intent, single responsibility
   */
  private async findToDoEntryDate(issueKey: string, toDoStatusIds: readonly string[]): Promise<string | null> {
    return await this.changelogApi.findStatusTransitionDateById(issueKey, toDoStatusIds);
  }

  /**
   * Calculates difference between two dates in days
   * Following Clean Code: Express intent, pure function
   */
  private calculateDaysDifference(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const diffInMs = end.getTime() - start.getTime();
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
    
    // Round to 1 decimal place
    return Math.round(diffInDays * 10) / 10;
  }

  /**
   * Calculates cycle times for multiple issues
   * Following Clean Code: Express intent, batch processing
   */
  async calculateBatchCycleTimes(
    boardId: string, 
    maxResults: number = 20
  ): Promise<IssueCycleTime[]> {
    console.log(`Starting cycle time calculation for board ${boardId}, max ${maxResults} issues`);
    
    try {
      // Get board configuration
      const columnConfig = await this.getBoardColumnConfig(boardId);
      console.log(`Board config: To Do statuses: ${columnConfig.toDoStatusIds.length}, Done statuses: ${columnConfig.doneStatusIds.length}`);
      
      // Fetch board issues
      const issues = await this.fetchBoardIssues(boardId, maxResults);
      console.log(`Fetched ${issues.length} issues from board ${boardId}`);
      
      // Calculate cycle time for each issue
      const cycleTimes: IssueCycleTime[] = [];
      
      for (const issue of issues) {
        const cycleTime = await this.calculateIssueCycleTime(
          issue.key,
          boardId,
          issue.fields.issuetype.name,
          columnConfig
        );
        
        cycleTimes.push(cycleTime);
        
        // Log result
        if (cycleTime.cycleTimeDays !== null) {
          console.log(`Issue ${issue.key}: ${cycleTime.cycleTimeDays} days (${cycleTime.issueType})`);
        } else {
          console.log(`Issue ${issue.key}: null (in progress) (${cycleTime.issueType})`);
        }
      }
      
      const completedCount = cycleTimes.filter(ct => ct.cycleTimeDays !== null).length;
      const inProgressCount = cycleTimes.length - completedCount;
      
      console.log(`Processed ${cycleTimes.length} issues, ${completedCount} completed, ${inProgressCount} in progress`);
      
      return cycleTimes;
    } catch (error) {
      console.error(`Failed to calculate batch cycle times for board ${boardId}:`, error);
      throw error;
    }
  }
}
