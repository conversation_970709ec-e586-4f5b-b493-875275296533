---
/**
 * Board Metrics Card Component
 * Displays individual board metrics in an attractive card format
 * Following Clean Code: Single responsibility, clear presentation
 */

export interface Props {
  boardId: string;
  boardName: string;
  averageVelocity: number;
  predictability: number;
  trend: 'up' | 'down' | 'stable' | 'no-data';
  sprintsAnalyzed: number;
  lastCalculated: string;
}

const { 
  boardId, 
  boardName, 
  averageVelocity, 
  predictability, 
  trend, 
  sprintsAnalyzed, 
  lastCalculated 
} = Astro.props;

// Trend colors and icons
const trendColors = {
  up: 'text-green-500',
  down: 'text-red-500',
  stable: 'text-blue-500',
  'no-data': 'text-gray-400'
};

const trendIcons = {
  up: '↗',
  down: '↘',
  stable: '→',
  'no-data': '—'
};

// Format last calculated date
const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return 'N/A';
  }
};

// Predictability color based on percentage
const getPredictabilityColor = (value: number) => {
  if (value >= 80) return 'text-green-600';
  if (value >= 60) return 'text-yellow-600';
  return 'text-red-600';
};
---

<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
  <!-- Header with board name and trend -->
  <div class="flex items-center justify-between mb-4">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 truncate" title={boardName}>
        {boardName}
      </h3>
      <p class="text-sm text-gray-500">Board ID: {boardId}</p>
    </div>
    <div class={`text-2xl ${trendColors[trend]}`} title={`Trend: ${trend}`}>
      {trendIcons[trend]}
    </div>
  </div>

  <!-- Metrics Grid -->
  <div class="grid grid-cols-2 gap-4 mb-4">
    <!-- Average Velocity -->
    <div class="text-center">
      <div class="text-2xl font-bold text-blue-600">
        {averageVelocity}
      </div>
      <div class="text-xs text-gray-500 uppercase tracking-wide">
        Avg Velocity
      </div>
    </div>

    <!-- Predictability -->
    <div class="text-center">
      <div class={`text-2xl font-bold ${getPredictabilityColor(predictability)}`}>
        {predictability}%
      </div>
      <div class="text-xs text-gray-500 uppercase tracking-wide">
        Predictability
      </div>
    </div>
  </div>

  <!-- Footer with sprints and date -->
  <div class="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100">
    <span>
      <strong>{sprintsAnalyzed}</strong> sprints analyzed
    </span>
    <span>
      Updated: {formatDate(lastCalculated)}
    </span>
  </div>
</div>
