<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Tasks Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #212529;
            line-height: 1.5;
            padding: 15px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #343a40;
            color: white;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            font-size: 1.75rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .header p {
            font-size: 0.95rem;
            opacity: 0.8;
            margin: 0;
        }

        .metadata {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metadata-item {
            text-align: center;
        }

        .metadata-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 3px;
        }

        .metadata-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
        }

        .table-container {
            padding: 0;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        thead {
            background: #495057;
            color: white;
        }

        th {
            padding: 12px 10px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border-bottom: 2px solid #dee2e6;
        }

        tbody tr {
            border-bottom: 1px solid #dee2e6;
        }

        tbody tr:hover {
            background: #f8f9fa;
        }

        td {
            padding: 10px;
            vertical-align: top;
            font-size: 0.9rem;
        }

        .task-id {
            font-weight: 700;
            color: #495057;
            font-size: 0.9rem;
            font-family: 'Courier New', monospace;
        }

        .task-title {
            font-weight: 600;
            color: #212529;
            margin-bottom: 3px;
            font-size: 0.95rem;
        }

        .task-description {
            color: #6c757d;
            line-height: 1.4;
            font-size: 0.85rem;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .status-done {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-in-progress {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-pending {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .category-badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-top: 2px;
        }

        .category-infrastructure { background: #e9ecef; color: #495057; }
        .category-database { background: #f8d7da; color: #721c24; }
        .category-authentication { background: #d1ecf1; color: #0c5460; }
        .category-ui-ux { background: #d4edda; color: #155724; }
        .category-data-model { background: #fff3cd; color: #856404; }
        .category-trip-discovery { background: #cce5ff; color: #004085; }
        .category-media { background: #f0e6ff; color: #6f42c1; }
        .category-social-features { background: #ffe6cc; color: #d63384; }
        .category-file-management { background: #e2f3f5; color: #0f5132; }
        .category-trip-download { background: #ffeaa7; color: #856404; }
        .category-maps { background: #d1f2eb; color: #0f5132; }
        .category-content-creation { background: #fce4ec; color: #ad1457; }
        .category-content-validation { background: #fff8e1; color: #e65100; }
        .category-ai-integration { background: #e8f5e8; color: #2e7d32; }
        .category-trip-builder { background: #e3f2fd; color: #1565c0; }
        .category-trip-customization { background: #f3e5f5; color: #7b1fa2; }
        .category-trip-preview { background: #e0f2f1; color: #00695c; }
        .category-content-publishing { background: #fff3e0; color: #ef6c00; }
        .category-user-management { background: #fce4ec; color: #c2185b; }
        .category-testing { background: #f1f8e9; color: #558b2f; }
        .category-devops { background: #e8eaf6; color: #3f51b5; }
        .category-poi-management { background: #e0f7fa; color: #00838f; }
        .category-gpx-management { background: #f9fbe7; color: #689f38; }

        .hours-badge {
            background: #6c757d;
            color: white;
            padding: 3px 6px;
            border-radius: 3px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .dependencies {
            color: #6c757d;
            font-size: 0.8rem;
            font-style: italic;
        }

        .user-story {
            background: #e9ecef;
            color: #495057;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.75rem;
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .container {
                margin: 5px;
                border-radius: 5px;
            }

            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .metadata {
                grid-template-columns: repeat(2, 1fr);
                padding: 10px 15px;
                gap: 10px;
            }

            th, td {
                padding: 8px 6px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Project Tasks Management</h1>
            <p>Comprehensive task tracking and project overview</p>
        </div>

        <div class="metadata">
            <div class="metadata-item">
                <div class="metadata-label">Total Tasks</div>
                <div class="metadata-value">45</div>
            </div>
            <div class="metadata-item">
                <div class="metadata-label">Estimated Hours</div>
                <div class="metadata-value">267h</div>
            </div>
            <div class="metadata-item">
                <div class="metadata-label">Last Updated</div>
                <div class="metadata-value">2025-06-25</div>
            </div>
            <div class="metadata-item">
                <div class="metadata-label">Next Task ID</div>
                <div class="metadata-value">TASK-046</div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title & Description</th>
                        <th>Status</th>
                        <th>Category</th>
                        <th>User Story</th>
                        <th>Hours</th>
                        <th>Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><div class="task-id">TASK-001</div></td>
                        <td>
                            <div class="task-title">Project Setup and Infrastructure</div>
                            <div class="task-description">Set up the basic project structure with Next.js, configure development environment, and establish core dependencies</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-infrastructure">Infrastructure</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">None</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-002</div></td>
                        <td>
                            <div class="task-title">Database Schema Design</div>
                            <div class="task-description">Design and implement the complete database schema for users, trips, POIs, GPX tracks, and media using Prisma and Supabase</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-database">Database</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">12h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-003</div></td>
                        <td>
                            <div class="task-title">Authentication Setup with Clerk</div>
                            <div class="task-description">Integrate Clerk for email, Apple, and Google authentication with user registration flow</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-authentication">Authentication</span></td>
                        <td><span class="user-story">US1.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-004</div></td>
                        <td>
                            <div class="task-title">User Registration Flow</div>
                            <div class="task-description">Implement rapid user registration process completing in under 60 seconds</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-authentication">Authentication</span></td>
                        <td><span class="user-story">US1.1</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-003, TASK-002</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-005</div></td>
                        <td>
                            <div class="task-title">Basic UI Components and Layout</div>
                            <div class="task-description">Create reusable UI components using TailwindCSS and establish the main application layout</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-ui-ux">UI/UX</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-006</div></td>
                        <td>
                            <div class="task-title">Trip Library Data Model</div>
                            <div class="task-description">Implement the data model for trips with all filterable attributes (destination, duration, difficulty, theme, road type)</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-data-model">Data Model</span></td>
                        <td><span class="user-story">US1.2</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-002</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-007</div></td>
                        <td>
                            <div class="task-title">Trip Library Filtering System</div>
                            <div class="task-description">Build the filtering interface and backend logic for trip discovery with all specified filter criteria</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-discovery">Trip Discovery</span></td>
                        <td><span class="user-story">US1.2</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-006, TASK-005</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-008</div></td>
                        <td>
                            <div class="task-title">Trip Library Display</div>
                            <div class="task-description">Create the trip library page with filtered results display showing title and description with links to detail pages</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-discovery">Trip Discovery</span></td>
                        <td><span class="user-story">US1.2</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-007</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-009</div></td>
                        <td>
                            <div class="task-title">Trip Detail Page Structure</div>
                            <div class="task-description">Create the trip detail page layout with sections for description, media, and action buttons</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-discovery">Trip Discovery</span></td>
                        <td><span class="user-story">US1.3</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-005</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-010</div></td>
                        <td>
                            <div class="task-title">Video Player Integration</div>
                            <div class="task-description">Implement video playback functionality for trip-associated videos on detail pages</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-media">Media</span></td>
                        <td><span class="user-story">US1.3</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-009</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-011</div></td>
                        <td>
                            <div class="task-title">Download and GPX Install Links</div>
                            <div class="task-description">Add download package and GPX installation links to trip detail pages</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-discovery">Trip Discovery</span></td>
                        <td><span class="user-story">US1.3</span></td>
                        <td><span class="hours-badge">3h</span></td>
                        <td><span class="dependencies">TASK-009</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-012</div></td>
                        <td>
                            <div class="task-title">Social Sharing Integration</div>
                            <div class="task-description">Implement social sharing buttons for Facebook, Instagram, and WhatsApp with public preview links</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-social-features">Social Features</span></td>
                        <td><span class="user-story">US1.4</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-009</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-013</div></td>
                        <td>
                            <div class="task-title">Public Trip Preview Mode</div>
                            <div class="task-description">Create public/preview mode for trip pages accessible via shared links</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-social-features">Social Features</span></td>
                        <td><span class="user-story">US1.4</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-012</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-014</div></td>
                        <td>
                            <div class="task-title">File Storage Setup</div>
                            <div class="task-description">Configure Supabase storage for GPX files, PDFs, images, and videos with proper access controls</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-file-management">File Management</span></td>
                        <td><span class="user-story">US1.5</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-002</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-015</div></td>
                        <td>
                            <div class="task-title">Package Generation System</div>
                            <div class="task-description">Build system to generate downloadable packages containing GPX, PDF, and video links</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-download">Trip Download</span></td>
                        <td><span class="user-story">US1.5</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-014</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-016</div></td>
                        <td>
                            <div class="task-title">PDF Generation for Trips</div>
                            <div class="task-description">Implement PDF generation with trip description, map, POI list, and recommended accommodations</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-download">Trip Download</span></td>
                        <td><span class="user-story">US1.5</span></td>
                        <td><span class="hours-badge">10h</span></td>
                        <td><span class="dependencies">TASK-015</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-017</div></td>
                        <td>
                            <div class="task-title">Mapbox Integration</div>
                            <div class="task-description">Integrate Mapbox for interactive maps displaying tracks and POIs</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-maps">Maps</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-018</div></td>
                        <td>
                            <div class="task-title">Ranger Dashboard Setup</div>
                            <div class="task-description">Create ranger dashboard with trip creation and management interface</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US4.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-005, TASK-003</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-019</div></td>
                        <td>
                            <div class="task-title">Basic Trip Creation Form</div>
                            <div class="task-description">Implement form for creating trips with title, summary, destination, duration, difficulty, tags, and season</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US4.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-018</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-020</div></td>
                        <td>
                            <div class="task-title">Trip State Management</div>
                            <div class="task-description">Implement trip state system (Draft, Ready for Review, Published) with automatic slug generation</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US4.1</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-019</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-021</div></td>
                        <td>
                            <div class="task-title">File Upload System</div>
                            <div class="task-description">Build file upload system for GPX (≤20MB), images (≥1280×720px), and videos (≤500MB) with drag-and-drop</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-file-management">File Management</span></td>
                        <td><span class="user-story">US4.2</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-014, TASK-018</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-022</div></td>
                        <td>
                            <div class="task-title">Media-POI Association</div>
                            <div class="task-description">Implement system to associate uploaded media with waypoints/POIs on the map via click interaction</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US4.2</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-021, TASK-017</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-023</div></td>
                        <td>
                            <div class="task-title">POI Creation Interface</div>
                            <div class="task-description">Build interface for creating POIs with map click/long-press, coordinate search, title, and description (max 250 chars)</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-poi-management">POI Management</span></td>
                        <td><span class="user-story">US5.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-017, TASK-018</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-024</div></td>
                        <td>
                            <div class="task-title">POI-Trip Association</div>
                            <div class="task-description">Implement system to associate POIs with trips and display them in trip POI summary</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-poi-management">POI Management</span></td>
                        <td><span class="user-story">US5.1</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-023</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-025</div></td>
                        <td>
                            <div class="task-title">GPX Upload and Validation</div>
                            <div class="task-description">Implement GPX file upload with basic validation and georeferenced map display</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-gpx-management">GPX Management</span></td>
                        <td><span class="user-story">US5.2</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-021, TASK-017</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-026</div></td>
                        <td>
                            <div class="task-title">GPX-Trip Association</div>
                            <div class="task-description">Build system to associate GPX tracks with trips and save them in Draft state</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-gpx-management">GPX Management</span></td>
                        <td><span class="user-story">US5.2</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-025, TASK-020</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-027</div></td>
                        <td>
                            <div class="task-title">GPX Validation Engine</div>
                            <div class="task-description">Build automatic validation for GPX files ensuring continuous tracks >5km without coordinate errors</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-validation">Content Validation</span></td>
                        <td><span class="user-story">US5.3</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-025</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-028</div></td>
                        <td>
                            <div class="task-title">Media Validation System</div>
                            <div class="task-description">Implement validation for image and video format, dimensions, and file size requirements</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-validation">Content Validation</span></td>
                        <td><span class="user-story">US5.3</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-021</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-029</div></td>
                        <td>
                            <div class="task-title">Validation Error Reporting</div>
                            <div class="task-description">Create detailed error messaging system for validation failures and state transition to 'Ready for Review'</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-validation">Content Validation</span></td>
                        <td><span class="user-story">US5.3</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-027, TASK-028</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-030</div></td>
                        <td>
                            <div class="task-title">Individual POI Creation</div>
                            <div class="task-description">Build standalone POI creation with map coordinates, title, description, and photo upload</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US5.4</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-023, TASK-021</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-031</div></td>
                        <td>
                            <div class="task-title">GPX Segment Upload</div>
                            <div class="task-description">Implement upload system for GPX segments ≤30km with tagging and description capabilities</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US5.4</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-025</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-032</div></td>
                        <td>
                            <div class="task-title">Content Indexing for Trip Builder</div>
                            <div class="task-description">Build indexing system to make new POIs and segments available to Trip Builder within 15 minutes</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-creation">Content Creation</span></td>
                        <td><span class="user-story">US5.4</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-030, TASK-031</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-033</div></td>
                        <td>
                            <div class="task-title">OpenRouter AI Integration</div>
                            <div class="task-description">Integrate OpenRouter API for LLM-powered trip planning and route optimization</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-ai-integration">AI Integration</span></td>
                        <td><span class="user-story">US4.3</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-034</div></td>
                        <td>
                            <div class="task-title">Custom Trip Parameters Form</div>
                            <div class="task-description">Create form for custom trip creation with duration, destination, max transfer time, and daily km limits</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-builder">Trip Builder</span></td>
                        <td><span class="user-story">US4.3</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-005</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-035</div></td>
                        <td>
                            <div class="task-title">Trip Builder AI Engine</div>
                            <div class="task-description">Build AI-powered trip builder that suggests optimal combinations from existing RideAtlas trips</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-builder">Trip Builder</span></td>
                        <td><span class="user-story">US9.1</span></td>
                        <td><span class="hours-badge">12h</span></td>
                        <td><span class="dependencies">TASK-033, TASK-034</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-036</div></td>
                        <td>
                            <div class="task-title">Transfer Distance Validation</div>
                            <div class="task-description">Implement warning system for transfers >30km between suggested trips</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-builder">Trip Builder</span></td>
                        <td><span class="user-story">US9.1</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-035</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-037</div></td>
                        <td>
                            <div class="task-title">POI Selection Interface</div>
                            <div class="task-description">Build interface for selecting/modifying POIs with color coding and add/remove functionality</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-customization">Trip Customization</span></td>
                        <td><span class="user-story">US5.5</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-023, TASK-017</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-038</div></td>
                        <td>
                            <div class="task-title">Track Segment Management</div>
                            <div class="task-description">Implement interface for managing track segments and accommodation structures in custom trips</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-customization">Trip Customization</span></td>
                        <td><span class="user-story">US5.5</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-025, TASK-037</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-039</div></td>
                        <td>
                            <div class="task-title">Trip Preview Interface</div>
                            <div class="task-description">Create trip preview with complete route map, distance summary, and confirmation options</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-trip-preview">Trip Preview</span></td>
                        <td><span class="user-story">US6.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-017, TASK-035</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-040</div></td>
                        <td>
                            <div class="task-title">Publication Scheduling System</div>
                            <div class="task-description">Build scheduling system with calendar interface for trip publication and social promotion</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-publishing">Content Publishing</span></td>
                        <td><span class="user-story">US7.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-020</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-041</div></td>
                        <td>
                            <div class="task-title">Subscriber Notification System</div>
                            <div class="task-description">Implement notification system for scheduled trip publications to subscribers</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-content-publishing">Content Publishing</span></td>
                        <td><span class="user-story">US7.1</span></td>
                        <td><span class="hours-badge">4h</span></td>
                        <td><span class="dependencies">TASK-040</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-042</div></td>
                        <td>
                            <div class="task-title">User Role Management</div>
                            <div class="task-description">Implement user role system with Ranger Junior/Senior roles and promotion capabilities</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-user-management">User Management</span></td>
                        <td><span class="user-story">US8.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-003</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-043</div></td>
                        <td>
                            <div class="task-title">Ranger Certification Interface</div>
                            <div class="task-description">Build interface for user search, role assignment, notifications, and admin revocation</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-user-management">User Management</span></td>
                        <td><span class="user-story">US8.1</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-042</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-044</div></td>
                        <td>
                            <div class="task-title">Testing Infrastructure</div>
                            <div class="task-description">Set up comprehensive testing framework with unit, integration, and E2E tests</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-testing">Testing</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">8h</span></td>
                        <td><span class="dependencies">TASK-001</span></td>
                    </tr>
                    <tr>
                        <td><div class="task-id">TASK-045</div></td>
                        <td>
                            <div class="task-title">Deployment and CI/CD</div>
                            <div class="task-description">Configure deployment pipeline and continuous integration for production deployment</div>
                        </td>
                        <td><span class="status-badge status-done">Done</span></td>
                        <td><span class="category-badge category-devops">DevOps</span></td>
                        <td><span class="user-story">FOUNDATION</span></td>
                        <td><span class="hours-badge">6h</span></td>
                        <td><span class="dependencies">TASK-044</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
