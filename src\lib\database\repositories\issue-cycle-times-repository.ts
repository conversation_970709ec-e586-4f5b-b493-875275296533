/**
 * Issue Cycle Times Repository for Turso database
 * Following Clean Architecture: Repository pattern for data persistence
 * Following Clean Code: Single responsibility, express intent
 */

import type { LibSQLDatabase } from 'drizzle-orm/libsql';
import { eq, and, sql } from 'drizzle-orm';
import { issueCycleTimes } from '../schemas/turso-schema.js';
import type { IssueCycleTime } from '../../kanban/cycle-time-calculator.js';
import type { IIssueCycleTimesRepository } from './interfaces.js';

/**
 * Database entity for issue cycle times
 * Following Clean Code: Data structure separation from domain model
 */
export interface IssueCycleTimeEntity {
  readonly id: number;
  readonly issueKey: string;
  readonly boardId: string;
  readonly startDate: string | null;
  readonly completionDate: string | null;
  readonly cycleTimeDays: number | null;
  readonly issueType: string;
  readonly createdAt: string;
  readonly updatedAt: string;
}

/**
 * Repository for issue cycle times operations
 * Following Clean Architecture: Repository pattern, dependency injection
 */
export class IssueCycleTimesRepository implements IIssueCycleTimesRepository {
  constructor(private readonly db: LibSQLDatabase<any>) {}

  /**
   * Saves or updates an issue cycle time record
   * Following Clean Code: Command-Query Separation (command)
   */
  async saveIssueCycleTime(cycleTime: IssueCycleTime): Promise<void> {
    try {
      await this.db.insert(issueCycleTimes).values({
        issueKey: cycleTime.issueKey,
        boardId: cycleTime.boardId,
        startDate: cycleTime.startDate,
        completionDate: cycleTime.completionDate,
        cycleTimeDays: cycleTime.cycleTimeDays,
        issueType: cycleTime.issueType,
        // createdAt and updatedAt will use SQL defaults
      }).onConflictDoUpdate({
        target: [issueCycleTimes.issueKey, issueCycleTimes.boardId],
        set: {
          startDate: cycleTime.startDate,
          completionDate: cycleTime.completionDate,
          cycleTimeDays: cycleTime.cycleTimeDays,
          issueType: cycleTime.issueType,
          updatedAt: sql`(datetime('now'))`,
        }
      });
    } catch (error) {
      throw new Error(`Failed to save cycle time for ${cycleTime.issueKey}: ${error}`);
    }
  }

  /**
   * Saves multiple issue cycle time records in batch
   * Following Clean Code: Batch processing, single responsibility
   */
  async saveBatchCycleTimes(cycleTimes: IssueCycleTime[]): Promise<void> {
    if (cycleTimes.length === 0) {
      return;
    }

    try {
      // Use transaction for batch insert
      await this.db.transaction(async (tx) => {
        for (const cycleTime of cycleTimes) {
          await tx.insert(issueCycleTimes).values({
            issueKey: cycleTime.issueKey,
            boardId: cycleTime.boardId,
            startDate: cycleTime.startDate,
            completionDate: cycleTime.completionDate,
            cycleTimeDays: cycleTime.cycleTimeDays,
            issueType: cycleTime.issueType,
            // createdAt and updatedAt will use SQL defaults
          }).onConflictDoUpdate({
            target: [issueCycleTimes.issueKey, issueCycleTimes.boardId],
            set: {
              startDate: cycleTime.startDate,
              completionDate: cycleTime.completionDate,
              cycleTimeDays: cycleTime.cycleTimeDays,
              issueType: cycleTime.issueType,
              updatedAt: sql`(datetime('now'))`,
            }
          });
        }
      });

      console.log(`Saved ${cycleTimes.length} cycle time records to database`);
    } catch (error) {
      throw new Error(`Failed to save batch cycle times: ${error}`);
    }
  }

  /**
   * Gets cycle time for a specific issue
   * Following Clean Code: Command-Query Separation (query)
   */
  async getIssueCycleTime(issueKey: string, boardId: string): Promise<IssueCycleTime | null> {
    try {
      const results = await this.db
        .select()
        .from(issueCycleTimes)
        .where(and(
          eq(issueCycleTimes.issueKey, issueKey),
          eq(issueCycleTimes.boardId, boardId)
        ))
        .limit(1);

      return results.length > 0 ? this.mapToIssueCycleTime(results[0]) : null;
    } catch (error) {
      throw new Error(`Failed to get cycle time for ${issueKey}: ${error}`);
    }
  }

  /**
   * Gets all cycle times for a board
   * Following Clean Code: Command-Query Separation (query)
   */
  async getBoardCycleTimes(boardId: string): Promise<IssueCycleTime[]> {
    try {
      const results = await this.db
        .select()
        .from(issueCycleTimes)
        .where(eq(issueCycleTimes.boardId, boardId));

      return results.map(result => this.mapToIssueCycleTime(result));
    } catch (error) {
      throw new Error(`Failed to get cycle times for board ${boardId}: ${error}`);
    }
  }

  /**
   * Gets completed cycle times for a board (for percentile analysis)
   * Following Clean Code: Express intent, single responsibility
   */
  async getCompletedCycleTimes(boardId: string): Promise<IssueCycleTime[]> {
    try {
      const results = await this.db
        .select()
        .from(issueCycleTimes)
        .where(and(
          eq(issueCycleTimes.boardId, boardId),
          // Only get completed issues (with cycle time)
          // Note: In SQLite, we need to check for NOT NULL
        ));

      // Filter completed issues in application layer since SQLite handling varies
      return results
        .map(result => this.mapToIssueCycleTime(result))
        .filter(cycleTime => cycleTime.cycleTimeDays !== null);
    } catch (error) {
      throw new Error(`Failed to get completed cycle times for board ${boardId}: ${error}`);
    }
  }

  /**
   * Checks if cycle time data exists for an issue
   * Following Clean Code: Express intent, boolean query
   */
  async existsIssueCycleTime(issueKey: string, boardId: string): Promise<boolean> {
    try {
      const results = await this.db
        .select({ count: issueCycleTimes.id })
        .from(issueCycleTimes)
        .where(and(
          eq(issueCycleTimes.issueKey, issueKey),
          eq(issueCycleTimes.boardId, boardId)
        ))
        .limit(1);

      return results.length > 0;
    } catch (error) {
      console.error(`Failed to check existence for ${issueKey}:`, error);
      return false;
    }
  }

  /**
   * Deletes cycle time record for an issue
   * Following Clean Code: Command-Query Separation (command)
   */
  async deleteIssueCycleTime(issueKey: string, boardId: string): Promise<void> {
    try {
      await this.db
        .delete(issueCycleTimes)
        .where(and(
          eq(issueCycleTimes.issueKey, issueKey),
          eq(issueCycleTimes.boardId, boardId)
        ));
    } catch (error) {
      throw new Error(`Failed to delete cycle time for ${issueKey}: ${error}`);
    }
  }

  /**
   * Deletes all cycle time records for a board
   * Following Clean Code: Command-Query Separation (command)
   */
  async deleteBoardCycleTimes(boardId: string): Promise<void> {
    try {
      await this.db
        .delete(issueCycleTimes)
        .where(eq(issueCycleTimes.boardId, boardId));
    } catch (error) {
      throw new Error(`Failed to delete cycle times for board ${boardId}: ${error}`);
    }
  }

  /**
   * Maps database entity to domain model
   * Following Clean Code: Data transformation, single responsibility
   */
  private mapToIssueCycleTime(entity: any): IssueCycleTime {
    return {
      issueKey: entity.issueKey,
      boardId: entity.boardId,
      startDate: entity.startDate,
      completionDate: entity.completionDate,
      cycleTimeDays: entity.cycleTimeDays,
      issueType: entity.issueType,
    };
  }

  /**
   * Gets cycle time statistics for a board
   * Following Clean Code: Express intent, statistical analysis
   */
  async getCycleTimeStats(boardId: string): Promise<{
    totalIssues: number;
    completedIssues: number;
    inProgressIssues: number;
    averageCycleTime: number | null;
  }> {
    try {
      const cycleTimes = await this.getBoardCycleTimes(boardId);
      const completed = cycleTimes.filter(ct => ct.cycleTimeDays !== null);
      
      const averageCycleTime = completed.length > 0
        ? completed.reduce((sum, ct) => sum + (ct.cycleTimeDays || 0), 0) / completed.length
        : null;

      return {
        totalIssues: cycleTimes.length,
        completedIssues: completed.length,
        inProgressIssues: cycleTimes.length - completed.length,
        averageCycleTime: averageCycleTime ? Math.round(averageCycleTime * 10) / 10 : null,
      };
    } catch (error) {
      throw new Error(`Failed to get cycle time stats for board ${boardId}: ${error}`);
    }
  }
}
